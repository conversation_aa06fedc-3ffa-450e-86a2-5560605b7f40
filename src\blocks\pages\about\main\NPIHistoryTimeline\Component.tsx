'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { Calendar, Lightbulb, Users, Rocket, Award, Filter } from 'lucide-react'
import { motion } from 'framer-motion'
import { useParallax } from '@/hooks/useParallax'
import { ScrollReveal, StaggerContainer, StaggerItem } from '@/components/ui/npi-scroll-reveal'

interface TimelineEvent {
  year: string
  title: string
  description: string
  icon: React.ReactNode
  category: string
}

interface NPIHistoryTimelineProps {
  title?: string
  description?: string
  rationale?: string
  timeline?: TimelineEvent[]
}

export const NPIHistoryTimelineBlock: React.FC<NPIHistoryTimelineProps> = ({
  title = 'History & Rationale',
  description = "The journey of transforming Kenya's natural heritage into a cornerstone of sustainable development.",
  rationale = 'Kenya is endowed with rich biodiversity and centuries of indigenous knowledge in natural products. However, this wealth remained largely untapped due to lack of systematic documentation, limited commercialization pathways, and inadequate protection of intellectual property rights. The NPI Initiative was conceived to bridge this gap, creating a comprehensive framework that honors traditional knowledge while building modern, sustainable industries.',
  timeline = [
    {
      year: '2019',
      title: 'Conceptualization & Research',
      description:
        'Initial research and stakeholder consultations identified the need for a comprehensive natural products initiative aligned with Kenya Vision 2030.',
      icon: <Lightbulb className="w-6 h-6" />,
      category: 'Planning',
    },
    {
      year: '2020',
      title: 'Multi-Agency Partnership Formation',
      description:
        'Strategic partnerships established with National Museums of Kenya, BeTA, and other key institutions to create a collaborative framework.',
      icon: <Users className="w-6 h-6" />,
      category: 'Partnership',
    },
    {
      year: '2021',
      title: 'Pilot Programs Launch',
      description:
        'First pilot programs launched in select counties to test methodologies for indigenous knowledge documentation and community engagement.',
      icon: <Rocket className="w-6 h-6" />,
      category: 'Implementation',
    },
    {
      year: '2022',
      title: 'Knowledge Documentation Platform Development',
      description:
        'Development of a comprehensive digital platform to systematically document and preserve traditional knowledge across Kenya.',
      icon: <Calendar className="w-6 h-6" />,
      category: 'Technology',
    },
    {
      year: '2023',
      title: 'National Expansion',
      description:
        'Expansion to all 47 counties with comprehensive documentation programs and community-based product development initiatives.',
      icon: <Award className="w-6 h-6" />,
      category: 'Scale-up',
    },
    {
      year: '2024',
      title: 'International Recognition',
      description:
        'NPI receives international recognition as a model for indigenous knowledge preservation and sustainable development in Africa.',
      icon: <Award className="w-6 h-6" />,
      category: 'Recognition',
    },
  ],
}) => {
  // Initialize with the first year from timeline
  const availableYears = Array.from(new Set(timeline.map((event) => event.year))).sort()
  const [selectedYear, setSelectedYear] = useState<string>(availableYears[0] || '2019')

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Planning':
        return 'bg-[#8D8F78] text-[#E5E1DC]'
      case 'Partnership':
        return 'bg-[#4C6444] text-[#E5E1DC]'
      case 'Implementation':
        return 'bg-[#A7795E] text-[#E5E1DC]'
      case 'Technology':
        return 'bg-[#6E3C19] text-[#E5E1DC]'
      case 'Scale-up':
        return 'bg-[#34170D] text-[#E5E1DC]'
      case 'Recognition':
        return 'bg-[#8A6240] text-[#E5E1DC]'
      default:
        return 'bg-[#CEC9BC] text-[#2F2C29]'
    }
  }

  // Get unique years for filter buttons (no "all" option)
  const uniqueYears = availableYears

  // Filter timeline to show only selected year
  const filteredTimeline = timeline.filter((event) => event.year === selectedYear)

  const { ref: parallaxRef, y: parallaxY, isMounted } = useParallax({ speed: 0.3 })

  return (
    <NPISection size="tight" variant="gradient-subtle" className="relative overflow-hidden">
      {/* Parallax Background Elements */}
      {isMounted && (
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            style={{ transform: `translateY(${parallaxY}px)` }}
            className="absolute top-1/4 right-1/5 w-64 h-64 bg-primary/5 blur-3xl rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
          <motion.div
            style={{ transform: `translateY(${parallaxY}px)` }}
            className="absolute bottom-1/4 left-1/5 w-80 h-80 bg-secondary/4 blur-3xl rounded-full"
            animate={{
              scale: [1.2, 1, 1.2],
              x: [0, 30, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 2,
            }}
          />
        </div>
      )}

      <div className="relative z-10">
        <NPISectionHeader>
          <NPISectionTitle>{title}</NPISectionTitle>
          <NPISectionDescription>{description}</NPISectionDescription>
        </NPISectionHeader>

        {/* Rationale */}
        <div className="mb-10">
          <ScrollReveal direction="up" delay={0.2}>
            <NPICard className="bg-gradient-to-r from-primary/5 to-secondary/5 h-[200px] w-full flex flex-col justify-center">
              <NPICardHeader>
                <NPICardTitle className="text-2xl text-center">Why NPI Was Created</NPICardTitle>
              </NPICardHeader>
              <NPICardContent className="flex-1 flex items-center">
                <p className="text-lg leading-relaxed text-center max-w-4xl mx-auto font-npi">
                  {rationale}
                </p>
              </NPICardContent>
            </NPICard>
          </ScrollReveal>
        </div>

        {/* Timeline */}
        <div className="relative">
          <h3 className="text-2xl font-bold text-center mb-8 font-npi">Our Journey</h3>

          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Side Filter */}
              <div className="lg:w-1/4 w-full">
                <div className="sticky top-24 bg-gradient-to-br from-[#EFE3BA] to-[#FFFFFF] p-4 shadow-lg border-2 border-[#725242] h-[320px] w-full flex flex-col">
                  <div className="flex items-center gap-2 mb-4">
                    <Filter className="w-5 h-5 text-[#000000]" />
                    <span className="text-lg font-semibold text-[#000000]">Select Year</span>
                  </div>
                  <div className="mb-4">
                    <div className="text-sm text-[#725242] mb-2">Currently Viewing:</div>
                    <div className="text-2xl font-bold text-[#000000] mb-4">{selectedYear}</div>
                  </div>
                  <div className="space-y-2 flex-1">
                    {uniqueYears.map((year) => (
                      <NPIButton
                        key={year}
                        size="sm"
                        variant={selectedYear === year ? 'primary' : 'outline'}
                        onClick={() => setSelectedYear(year)}
                        className="w-full justify-start text-sm"
                      >
                        {year}
                      </NPIButton>
                    ))}
                  </div>
                </div>
              </div>

              {/* Content Area */}
              <div className="lg:w-3/4 w-full">
                <StaggerContainer>
                  <div className="grid gap-6">
                    {filteredTimeline.map((event, index) => (
                      <StaggerItem key={index} direction="up">
                        <NPICard className="hover:shadow-lg transition-shadow duration-300 h-[320px] w-full flex flex-col">
                          <NPICardHeader>
                            <div className="flex items-center gap-4 mb-4">
                              <div className="w-16 h-16 bg-gradient-to-br from-[#725242] to-[#8A3E25] flex items-center justify-center text-[#FFFFFF] shadow-lg flex-shrink-0">
                                {event.icon}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-3xl font-bold text-[#000000] font-npi">
                                    {event.year}
                                  </span>
                                  <span
                                    className={`px-4 py-2 text-sm font-medium ${getCategoryColor(event.category)}`}
                                  >
                                    {event.category}
                                  </span>
                                </div>
                                <NPICardTitle className="text-xl mb-2">{event.title}</NPICardTitle>
                              </div>
                            </div>
                          </NPICardHeader>
                          <NPICardContent className="flex-1 flex flex-col justify-between">
                            <div className="flex-1">
                              <p className="text-[#725242] leading-relaxed font-npi text-base mb-4">
                                {event.description}
                              </p>
                            </div>
                            {/* Additional detailed information for focused year view */}
                            <div className="bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] p-4 border border-[#725242]/30 mt-auto">
                              <h4 className="text-sm font-semibold text-[#000000] mb-2">
                                Key Achievements:
                              </h4>
                              <ul className="text-sm text-[#725242] space-y-1">
                                <li>• Strategic partnerships established</li>
                                <li>• Community engagement programs launched</li>
                                <li>• Knowledge documentation systems implemented</li>
                              </ul>
                            </div>
                          </NPICardContent>
                        </NPICard>
                      </StaggerItem>
                    ))}
                  </div>
                </StaggerContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </NPISection>
  )
}
