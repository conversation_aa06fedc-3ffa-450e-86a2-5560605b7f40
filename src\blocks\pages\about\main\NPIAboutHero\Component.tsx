import React from 'react'
import Image from 'next/image'

interface NPIAboutHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIAboutHeroBlock: React.FC<NPIAboutHeroProps> = ({
  title = 'About the Natural Products Industry Initiative',
  backgroundImage = '/assets/brian-kungu-SAs4ROPC7XI-unsplash.jpg',
}) => {
  return (
    <section className="relative min-h-[85vh] max-h-[95vh] -mt-24 pt-24">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#102820]/30 via-[#4C6444]/50 to-[#2F2C29]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white">
          {title}
        </h1>
      </div>

      {/* Bottom Left Text with bright colors */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#E5E1DC]/90 backdrop-blur-md p-6 border-l-4 border-[#8D8F78]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-[#2F2C29]">
            Kenya&apos;s rich biodiversity holds immense potential. We&apos;re dedicated to
            unlocking it sustainably.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with homepage colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#EFE3BA]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#725242]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#000000]">Our Mission</h3>
          <p className="text-sm sm:text-base mb-4 text-[#725242]">
            Empowering local communities through natural product innovation and sustainable
            practices.
          </p>
          <a
            href="#"
            className="text-[#8A3E25] hover:text-[#25718A] text-sm font-medium transition-colors"
          >
            Learn More &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
