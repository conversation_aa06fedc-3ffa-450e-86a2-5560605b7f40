'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { ArrowRight, Target, Eye, Heart, Users, MapPin, TrendingUp } from 'lucide-react'
import { motion } from 'framer-motion'

interface NPIIntroductionProps {
  title?: string
  description?: string
  content?: string
}

export const NPIIntroductionBlock: React.FC<NPIIntroductionProps> = ({
  title = 'About the Natural Products Industry Initiative',
  description = "Transforming Kenya's Vision 2030 into reality through sustainable natural products development.",
  content = "The Natural Products Industry (NPI) Initiative is a groundbreaking multi-agency program designed to harness Kenya's rich indigenous knowledge and natural resources for sustainable economic development. Aligned with Kenya Vision 2030 and the Medium Term Plan IV (MTP IV), NPI serves as a catalyst for transforming traditional knowledge into market-ready products while preserving cultural heritage and empowering local communities.",
}) => {
  const highlights = [
    {
      icon: <Target className="w-6 h-6" />,
      title: 'Strategic Alignment',
      description:
        'Directly supports Kenya Vision 2030 and MTP IV objectives for sustainable development.',
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: 'Innovation Focus',
      description:
        'Combines traditional knowledge with modern science and technology for product development.',
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: 'Community Centered',
      description:
        'Ensures local communities are central to and benefit from all natural product initiatives.',
    },
  ]

  return (
    <NPISection className="bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC] to-[#CABA9C] relative overflow-hidden -mt-4 pt-8">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-[#4C6444]/20 blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#A7795E]/15 blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, 25, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-6 py-3 bg-[#725242]/20 border-2 border-[#725242] text-[#000000] text-sm font-medium mb-8"
          >
            <span className="w-2 h-2 bg-[#725242] mr-3"></span>
            Natural Products Industry Initiative
          </motion.div>
          <NPISectionTitle className="text-[#000000] font-bold leading-[1.1] tracking-[-0.02em]">
            {title}
          </NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto font-light leading-[1.6]">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-6 mb-12">
            {/* Content Column */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="bg-white/80 backdrop-blur-sm p-4 shadow-lg border border-[#EFE3BA]/20 h-[400px] w-full flex flex-col"
              >
                <div className="prose prose-lg max-w-none mb-6 flex-shrink-0">
                  <p className="text-[#725242] leading-[1.6] font-npi text-base font-light">
                    {content}
                  </p>
                </div>

                <div className="grid gap-3 flex-1 overflow-hidden">
                  {highlights.map((highlight, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.1, duration: 0.6 }}
                      className="flex gap-3 p-2 bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-2 border-[#725242]/30 hover:border-[#725242] transition-all duration-300 group h-[70px] w-full"
                    >
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-[#725242] to-[#8A3E25] flex items-center justify-center text-[#FFFFFF] shadow-lg group-hover:scale-110 transition-transform duration-300">
                        {highlight.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-sm mb-1 font-npi text-[#000000] group-hover:text-[#8A3E25] transition-colors truncate">
                          {highlight.title}
                        </h3>
                        <p className="text-[#725242] font-npi leading-tight text-xs line-clamp-3">
                          {highlight.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="flex flex-col sm:flex-row gap-3 mt-4 flex-shrink-0">
                  <NPIButton
                    asChild
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200 text-sm"
                  >
                    <Link href="/about">
                      Learn More About NPI <ArrowRight className="w-3 h-3 ml-2" />
                    </Link>
                  </NPIButton>

                  <NPIButton
                    asChild
                    className="border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground font-medium px-4 py-2 transition-all duration-200 text-sm"
                  >
                    <Link href="/strategic-pillars">Explore Strategic Pillars</Link>
                  </NPIButton>
                </div>
              </motion.div>
            </div>

            {/* Statistics Sidebar */}
            <div className="space-y-3">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="bg-gradient-to-br from-[#725242] to-[#8A3E25] text-[#FFFFFF] p-3 shadow-lg h-[120px] w-full flex flex-col justify-center"
              >
                <div className="flex items-center gap-3 mb-4">
                  <MapPin className="w-6 h-6" />
                  <h3 className="font-semibold text-lg">47 Counties</h3>
                </div>
                <div className="text-3xl font-bold mb-2">Nationwide</div>
                <p className="text-[#FFFFFF]/80">Complete coverage across Kenya</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="bg-gradient-to-br from-[#8A3E25] to-[#725242] text-[#FFFFFF] p-3 shadow-lg h-[120px] w-full flex flex-col justify-center"
              >
                <div className="flex items-center gap-3 mb-4">
                  <Users className="w-6 h-6" />
                  <h3 className="font-semibold text-lg">2,500+ Members</h3>
                </div>
                <div className="text-3xl font-bold mb-2">Community</div>
                <p className="text-[#FFFFFF]/80">Directly engaged participants</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="bg-gradient-to-br from-[#25718A] to-[#8A3E25] border-2 border-[#725242] p-3 shadow-lg h-[120px] w-full flex flex-col justify-center"
              >
                <div className="flex items-center gap-3 mb-4">
                  <TrendingUp className="w-6 h-6 text-[#FFFFFF]" />
                  <h3 className="font-semibold text-lg text-[#FFFFFF]">85% Growth</h3>
                </div>
                <div className="text-3xl font-bold mb-2 text-[#FFFFFF]">Income</div>
                <p className="text-[#FFFFFF]/80">Average increase for communities</p>
              </motion.div>
            </div>
          </div>

          {/* Key Statistics */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid md:grid-cols-2 gap-6 mb-6"
          >
            <div className="text-center bg-gradient-to-br from-[#EFE3BA] to-[#FFFFFF] p-4 border-2 border-[#725242]/30 shadow-lg h-[120px] w-full flex flex-col justify-center">
              <div className="text-3xl font-bold text-[#000000] mb-1 font-npi">1,200+</div>
              <div className="text-sm text-[#725242] font-npi font-medium">
                Knowledge Assets Documented
              </div>
            </div>
            <div className="text-center bg-gradient-to-br from-[#EFE3BA] to-[#FFFFFF] p-4 border-2 border-[#8A3E25]/30 shadow-lg h-[120px] w-full flex flex-col justify-center">
              <div className="text-3xl font-bold text-[#000000] mb-1 font-npi">150+</div>
              <div className="text-sm text-[#725242] font-npi font-medium">
                Investment Opportunities
              </div>
            </div>
          </motion.div>

          {/* Partners Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="bg-gradient-to-br from-[#EFE3BA] to-[#FFFFFF] p-4 border-2 border-[#725242]/30 shadow-lg h-[120px] w-full flex flex-col justify-center"
          >
            <h3 className="text-[#000000] text-center text-xl font-semibold mb-4">
              Key Implementing Partners
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-[#725242] font-npi">
              <div className="flex items-center gap-2 p-2 bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-2 border-[#725242]/30 hover:border-[#725242] transition-colors group h-[50px] w-full">
                <div className="w-3 h-3 bg-[#725242] group-hover:scale-125 transition-transform flex-shrink-0"></div>
                <span className="text-sm font-medium">Organiser 1</span>
              </div>
              <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-2 border-[#8A3E25]/30 hover:border-[#8A3E25] transition-colors group h-[70px] w-full">
                <div className="w-3 h-3 bg-[#8A3E25] group-hover:scale-125 transition-transform flex-shrink-0"></div>
                <span className="text-sm font-medium">Organiser 2</span>
              </div>
              <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-2 border-[#25718A]/30 hover:border-[#25718A] transition-colors group h-[70px] w-full">
                <div className="w-3 h-3 bg-[#25718A] group-hover:scale-125 transition-transform flex-shrink-0"></div>
                <span className="text-sm font-medium">Organiser 3</span>
              </div>
              <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-2 border-[#725242]/30 hover:border-[#725242] transition-colors group h-[70px] w-full">
                <div className="w-3 h-3 bg-[#725242] group-hover:scale-125 transition-transform flex-shrink-0"></div>
                <span className="text-sm font-medium">Organiser 4</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </NPISection>
  )
}
