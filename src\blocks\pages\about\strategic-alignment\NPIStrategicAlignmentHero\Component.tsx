import React from 'react'
import Image from 'next/image'

interface NPIStrategicAlignmentHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIStrategicAlignmentHeroBlock: React.FC<NPIStrategicAlignmentHeroProps> = ({
  title = 'Strategic Alignment',
  subtitle = "Aligned with Kenya's key development frameworks",
  backgroundImage = '/assets/hero image.jpg',
}) => {
  return (
    <section className="relative min-h-[85vh] max-h-[95vh] -mt-24 pt-24">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Strategic Alignment background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with homepage colors */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#8A3E25]/30 via-[#725242]/50 to-[#EFE3BA]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white mb-4">
          {title}
        </h1>
        {subtitle && (
          <p className="text-lg sm:text-xl md:text-2xl text-white/90 font-medium">{subtitle}</p>
        )}
      </div>

      {/* Bottom Left Text with homepage colors */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#EFE3BA]/90 backdrop-blur-md p-6 border-l-4 border-[#8A3E25]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-[#000000]">
            Strategically aligned with Vision 2030, MTP IV, BeTA, and National Museums of Kenya
            frameworks.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with homepage colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#EFE3BA]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#8A3E25]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#000000]">National Alignment</h3>
          <p className="text-sm sm:text-base mb-4 text-[#725242]">
            Coordinated efforts toward national transformation goals and sustainable development.
          </p>
          <a
            href="#"
            className="text-[#8A3E25] hover:text-[#25718A] text-sm font-medium transition-colors"
          >
            Learn More &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
